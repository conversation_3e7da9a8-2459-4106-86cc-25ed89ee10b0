<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useSaleStore } from '../store/saleStore'
import { useCustomerStore } from '../store/customerStore'
import { useProductStore } from '../store/productStore'
import type { Sale, SaleItem } from '../types/sale'

const props = defineProps<{
  id?: string
  isNew?: boolean
}>()

const route = useRoute()
const router = useRouter()
const saleStore = useSaleStore()
const customerStore = useCustomerStore()
const productStore = useProductStore()

const isNewSale = computed(() => props.isNew || route.params.id === 'new')
const isLoading = ref(false)
const isSubmitting = ref(false)
const error = ref('')
const success = ref('')

// Yeni satış için varsayılan değerler
const sale = ref<Sale>({
  id: '',
  customerId: '',
  saleDate: new Date().toISOString().split('T')[0],
  items: [],
  subtotal: 0,
  taxRate: 18,
  taxAmount: 0,
  discountAmount: 0,
  finalAmount: 0,
  notes: '',
  paymentMethod: 'cash',
  paymentStatus: 'pending',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
})

// Yeni ürün ekleme için geçici değişken
const newItem = ref<SaleItem>({
  productId: '',
  quantity: 1,
  unitPrice: 0,
  discount: 0,
  total: 0
})

// Mevcut satışı yükle
onMounted(async () => {
  try {
    isLoading.value = true
    error.value = ''
    
    // Müşteri ve ürün verilerini yükle
    await Promise.all([
      customerStore.fetchCustomers(),
      productStore.fetchProducts()
    ])
    
    // Yeni satış değilse, mevcut satışı getir
    if (!isNewSale.value) {
      const saleId = route.params.id as string
      await saleStore.fetchSaleById(saleId)
      
      if (saleStore.currentSale) {
        sale.value = { ...saleStore.currentSale }
      } else {
        error.value = 'Satış bulunamadı'
        router.push('/sales')
      }
    }
  } catch (err) {
    error.value = 'Satış yüklenirken bir hata oluştu'
    console.error(err)
  } finally {
    isLoading.value = false
  }
})

// Ürün seçildiğinde fiyatı otomatik doldur
watch(() => newItem.value.productId, (newProductId) => {
  if (newProductId) {
    const product = productStore.products.find(p => p.id === newProductId)
    if (product) {
      newItem.value.unitPrice = product.price
      calculateItemTotal()
    }
  }
})

// Ürün miktarı veya fiyatı değiştiğinde toplamı hesapla
watch([() => newItem.value.quantity, () => newItem.value.unitPrice, () => newItem.value.discount], () => {
  calculateItemTotal()
})

// Satış öğelerindeki değişiklikleri izle ve satış toplamını güncelle
watch(() => sale.value.items, () => {
  calculateSaleTotals()
}, { deep: true })

// Vergi oranı veya indirim değiştiğinde toplamları güncelle
watch([() => sale.value.taxRate, () => sale.value.discountAmount], () => {
  calculateSaleTotals()
})

// Ürün toplam tutarını hesapla
const calculateItemTotal = () => {
  const quantity = newItem.value.quantity || 0
  const unitPrice = newItem.value.unitPrice || 0
  const discount = newItem.value.discount || 0
  
  newItem.value.total = (quantity * unitPrice) - discount
}

// Satış toplamlarını hesapla
const calculateSaleTotals = () => {
  // Alt toplam hesapla
  sale.value.subtotal = sale.value.items.reduce((sum, item) => sum + item.total, 0)
  
  // Vergi tutarını hesapla
  sale.value.taxAmount = (sale.value.subtotal * (sale.value.taxRate / 100))
  
  // Son tutarı hesapla
  sale.value.finalAmount = sale.value.subtotal + sale.value.taxAmount - (sale.value.discountAmount || 0)
}

// Ürün ekle
const addItem = () => {
  if (!newItem.value.productId || newItem.value.quantity <= 0) {
    error.value = 'Lütfen ürün seçin ve geçerli bir miktar girin'
    return
  }
  
  // Aynı ürün zaten varsa miktarını artır
  const existingItemIndex = sale.value.items.findIndex(item => item.productId === newItem.value.productId)
  
  if (existingItemIndex >= 0) {
    // Mevcut öğeyi güncelle
    const existingItem = sale.value.items[existingItemIndex]
    existingItem.quantity += newItem.value.quantity
    existingItem.total = (existingItem.quantity * existingItem.unitPrice) - (existingItem.discount || 0)
  } else {
    // Yeni öğe ekle
    sale.value.items.push({ ...newItem.value })
  }
  
  // Yeni öğe formunu temizle
  newItem.value = {
    productId: '',
    quantity: 1,
    unitPrice: 0,
    discount: 0,
    total: 0
  }
  
  // Hata mesajını temizle
  error.value = ''
}

// Ürünü kaldır
const removeItem = (index: number) => {
  sale.value.items.splice(index, 1)
}

// Ürün adını getir
const getProductName = (productId: string) => {
  const product = productStore.products.find(p => p.id === productId)
  return product ? product.name : 'Bilinmeyen Ürün'
}

// Satışı kaydet
const saveSale = async () => {
  if (!sale.value.customerId) {
    error.value = 'Lütfen bir müşteri seçin'
    return
  }
  
  if (sale.value.items.length === 0) {
    error.value = 'Satışa en az bir ürün eklemelisiniz'
    return
  }
  
  try {
    isSubmitting.value = true
    error.value = ''
    
    if (isNewSale.value) {
      // Yeni satış oluştur
      await saleStore.createSale(sale.value)
      success.value = 'Satış başarıyla oluşturuldu'
    } else {
      // Mevcut satışı güncelle
      await saleStore.updateSale(sale.value)
      success.value = 'Satış başarıyla güncellendi'
    }
    
    // Kısa bir süre sonra satış listesine dön
    setTimeout(() => {
      router.push('/sales')
    }, 1500)
  } catch (err) {
    error.value = 'Satış kaydedilirken bir hata oluştu'
    console.error(err)
  } finally {
    isSubmitting.value = false
  }
}

// İptal et ve listeye dön
const cancelEdit = () => {
  router.push('/sales')
}

// Ödeme durumu ve yöntemi için etiket ve renkler
const getPaymentStatusClass = (status: string) => {
  switch (status) {
    case 'paid': return 'status-paid'
    case 'pending': return 'status-pending'
    case 'cancelled': return 'status-cancelled'
    default: return 'status-pending'
  }
}

const getPaymentMethodLabel = (method: string) => {
  switch (method) {
    case 'cash': return 'Nakit'
    case 'credit_card': return 'Kredi Kartı'
    case 'bank_transfer': return 'Banka Transferi'
    case 'other': return 'Diğer'
    default: return 'Bilinmeyen'
  }
}

const getPaymentStatusLabel = (status: string) => {
  switch (status) {
    case 'paid': return 'Ödendi'
    case 'pending': return 'Beklemede'
    case 'cancelled': return 'İptal Edildi'
    default: return 'Bilinmeyen'
  }
}
</script>

<template>
  <div class="sale-detail-container">
    <div class="sale-header">
      <h1>{{ isNewSale ? 'Yeni Satış' : 'Satış Detayları' }}</h1>
      <div class="header-actions">
        <button class="btn" @click="cancelEdit">
          <span class="pi pi-times"></span>
          İptal
        </button>
        <button 
          class="btn btn-primary" 
          @click="saveSale" 
          :disabled="isSubmitting"
        >
          <span class="pi pi-save"></span>
          {{ isSubmitting ? 'Kaydediliyor...' : 'Kaydet' }}
        </button>
      </div>
    </div>

    <div v-if="isLoading" class="loading-container">
      <span class="pi pi-spin pi-spinner"></span>
      <p>Yükleniyor...</p>
    </div>

    <div v-else-if="error && !success" class="error-message">
      <span class="pi pi-exclamation-triangle"></span>
      <p>{{ error }}</p>
    </div>

    <div v-else-if="success" class="success-message">
      <span class="pi pi-check-circle"></span>
      <p>{{ success }}</p>
    </div>

    <div v-else class="sale-form-container">
      <!-- Satış Bilgileri -->
      <div class="card">
        <h2>Satış Bilgileri</h2>
        
        <div class="form-row">
          <div class="form-group">
            <label for="customer">Müşteri</label>
            <select 
              id="customer" 
              class="form-control" 
              v-model="sale.customerId"
              required
            >
              <option value="">Müşteri Seçin</option>
              <option 
                v-for="customer in customerStore.customers" 
                :key="customer.id" 
                :value="customer.id"
              >
                {{ customer.firstName }} {{ customer.lastName }}
              </option>
            </select>
          </div>

          <div class="form-group">
            <label for="saleDate">Satış Tarihi</label>
            <input 
              id="saleDate" 
              type="date" 
              class="form-control" 
              v-model="sale.saleDate"
              required
            />
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="paymentMethod">Ödeme Yöntemi</label>
            <select 
              id="paymentMethod" 
              class="form-control" 
              v-model="sale.paymentMethod"
            >
              <option value="cash">Nakit</option>
              <option value="credit_card">Kredi Kartı</option>
              <option value="bank_transfer">Banka Transferi</option>
              <option value="other">Diğer</option>
            </select>
          </div>

          <div class="form-group">
            <label for="paymentStatus">Ödeme Durumu</label>
            <select 
              id="paymentStatus" 
              class="form-control" 
              v-model="sale.paymentStatus"
            >
              <option value="paid">Ödendi</option>
              <option value="pending">Beklemede</option>
              <option value="cancelled">İptal Edildi</option>
            </select>
          </div>
        </div>

        <div class="form-group">
          <label for="notes">Notlar</label>
          <textarea 
            id="notes" 
            class="form-control" 
            v-model="sale.notes"
            rows="3"
            placeholder="Satış hakkında notlar..."
          ></textarea>
        </div>
      </div>

      <!-- Ürün Ekleme -->
      <div class="card">
        <h2>Ürünler</h2>
        
        <div class="add-item-form">
          <div class="form-row">
            <div class="form-group">
              <label for="product">Ürün</label>
              <select 
                id="product" 
                class="form-control" 
                v-model="newItem.productId"
              >
                <option value="">Ürün Seçin</option>
                <option 
                  v-for="product in productStore.products" 
                  :key="product.id" 
                  :value="product.id"
                >
                  {{ product.name }} ({{ product.price.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' }) }})
                </option>
              </select>
            </div>

            <div class="form-group">
              <label for="quantity">Miktar</label>
              <input 
                id="quantity" 
                type="number" 
                class="form-control" 
                v-model.number="newItem.quantity"
                min="1"
              />
            </div>

            <div class="form-group">
              <label for="unitPrice">Birim Fiyat</label>
              <input 
                id="unitPrice" 
                type="number" 
                class="form-control" 
                v-model.number="newItem.unitPrice"
                min="0"
                step="0.01"
              />
            </div>

            <div class="form-group">
              <label for="discount">İndirim</label>
              <input 
                id="discount" 
                type="number" 
                class="form-control" 
                v-model.number="newItem.discount"
                min="0"
                step="0.01"
              />
            </div>

            <div class="form-group">
              <label>Toplam</label>
              <div class="readonly-value">
                {{ newItem.total.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' }) }}
              </div>
            </div>
          </div>

          <button 
            class="btn btn-primary add-item-btn" 
            @click="addItem"
            :disabled="!newItem.productId || newItem.quantity <= 0"
          >
            <span class="pi pi-plus"></span>
            Ürün Ekle
          </button>
        </div>

        <!-- Ürün Listesi -->
        <div v-if="sale.items.length === 0" class="empty-items">
          <p>Henüz ürün eklenmedi</p>
        </div>

        <div v-else class="items-table-container">
          <table class="items-table">
            <thead>
              <tr>
                <th>Ürün</th>
                <th>Miktar</th>
                <th>Birim Fiyat</th>
                <th>İndirim</th>
                <th>Toplam</th>
                <th>İşlem</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, index) in sale.items" :key="index">
                <td>{{ getProductName(item.productId) }}</td>
                <td>
                  <input 
                    type="number" 
                    class="form-control" 
                    v-model.number="item.quantity"
                    min="1"
                  />
                </td>
                <td>
                  <input 
                    type="number" 
                    class="form-control" 
                    v-model.number="item.unitPrice"
                    min="0"
                    step="0.01"
                  />
                </td>
                <td>
                  <input 
                    type="number" 
                    class="form-control" 
                    v-model.number="item.discount"
                    min="0"
                    step="0.01"
                  />
                </td>
                <td class="item-total">
                  {{ item.total.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' }) }}
                </td>
                <td>
                  <button class="btn-icon" @click="removeItem(index)">
                    <span class="pi pi-trash"></span>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Satış Özeti -->
      <div class="card sale-summary">
        <h2>Satış Özeti</h2>
        
        <div class="summary-row">
          <span>Ara Toplam:</span>
          <span>{{ sale.subtotal.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' }) }}</span>
        </div>
        
        <div class="summary-row">
          <div class="tax-rate-input">
            <span>KDV (%):</span>
            <input 
              type="number" 
              class="form-control" 
              v-model.number="sale.taxRate"
              min="0"
              max="100"
              step="1"
            />
          </div>
          <span>{{ sale.taxAmount.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' }) }}</span>
        </div>
        
        <div class="summary-row">
          <div class="discount-input">
            <span>İndirim:</span>
            <input 
              type="number" 
              class="form-control" 
              v-model.number="sale.discountAmount"
              min="0"
              step="0.01"
            />
          </div>
          <span>-{{ sale.discountAmount.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' }) }}</span>
        </div>
        
        <div class="summary-row total">
          <span>Genel Toplam:</span>
          <span>{{ sale.finalAmount.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' }) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.sale-detail-container {
  width: 100%;
}

.sale-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.sale-header h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  gap: 1rem;
}

.loading-container .pi {
  font-size: 2rem;
  color: #D1D5DB;
}

.error-message,
.success-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  border-radius: 0.375rem;
  margin-bottom: 1.5rem;
}

.error-message {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
}

.success-message {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.sale-form-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  padding: 1.5rem;
}

.card h2 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color);
  margin-top: 0;
  margin-bottom: 1.25rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--border-color);
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-group {
  flex: 1;
  min-width: 200px;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.form-group label {
  font-size: 0.875rem;
  color: #6B7280;
}

.form-control {
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.form-control:focus {
  border-color: var(--primary-color);
  outline: none;
}

.readonly-value {
  padding: 0.5rem 0.75rem;
  background-color: #F9FAFB;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  font-size: 0.875rem;
  color: var(--text-color);
}

.add-item-form {
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.add-item-btn {
  margin-top: 1rem;
}

.empty-items {
  text-align: center;
  padding: 2rem;
  color: #6B7280;
}

.items-table-container {
  overflow-x: auto;
}

.items-table {
  width: 100%;
  border-collapse: collapse;
}

.items-table th,
.items-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.items-table th {
  font-weight: 600;
  color: #6B7280;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.items-table .form-control {
  width: 100%;
}

.item-total {
  font-weight: 500;
}

.btn-icon {
  background: none;
  border: none;
  cursor: pointer;
  width: 32px;
  height: 32px;
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6B7280;
  transition: all 0.2s ease;
}

.btn-icon:hover {
  background-color: #F3F4F6;
  color: var(--error-color);
}

.sale-summary {
  align-self: flex-end;
  width: 100%;
  max-width: 500px;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--border-color);
}

.summary-row:last-child {
  border-bottom: none;
}

.summary-row.total {
  font-weight: 600;
  font-size: 1.125rem;
  padding-top: 1rem;
}

.tax-rate-input,
.discount-input {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tax-rate-input .form-control,
.discount-input .form-control {
  width: 100px;
}

@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
  }
  
  .form-group {
    width: 100%;
  }
  
  .header-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .header-actions .btn {
    width: 100%;
  }
  
  .sale-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .sale-summary {
    max-width: 100%;
  }
}
</style>